<?php

$lines = explode("\n", shell_exec("cat ../data/*.txt")); // | tail -n1000
// var_dump($a);

function dataFromLine(string $line): array {
    $line = str_replace(" - ", " ", $line);
    $parts = explode(" ", $line);
    $parts[0] = str_replace('@', ' ', $parts[0]);
    $parts[0] = strtotime($parts[0]);
    return $parts;
}

//var_dump($parts);
//
//exit;

$placeHolders = implode(',', array_fill(0, 21, '?'));

$query = "INSERT INTO readings (
        time, 
        pi_from_buffer,
        pi_outside_temp,
        pi_from_pump,
        relay,
        Pump_Flow,
        Main_Inlet_Temp,
        Main_Outlet_Temp,
        Main_Target_Temp,
        Outside_Temp,
        Heat_Energy_Production,
        Heat_Energy_Consumption,
        Z1_Water_Temp,
        Buffer_Temp,
        Defrosting_State,
        Compressor_Freq,
        Operations_Counter,
        Z1_Heat_Request_Temp,
        Z1_Water_Target_Temp,
        Room_Thermostat_Temp,
        Internal_Heater_State) VALUES ($placeHolders)";

// print $query;

$conn = getConnection();

$conn->beginTransaction();

$stmt = $conn->prepare($query);

foreach ($lines as $line) {

    foreach (dataFromLine($line) as $idx => $part) {
        $stmt->bindValue($idx + 1, $part);
    }

    $stmt->execute();
}

$conn->commit();

function getConnection(): PDO {
    return new PDO('sqlite:readings.sqlite');
}
