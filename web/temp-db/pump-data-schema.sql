CREATE TABLE IF NOT EXISTS readings (
  time timestamp,
  ext_temp_1 integer,
  ext_temp_2 integer,
  ext_temp_3 integer,
  heatpump_state integer,
  defrosting_state integer,
  internal_heater_state integer,
  valve_state integer,
  pump_flow integer,
  inlet_temp integer,
  outlet_temp integer,
  target_temp integer,
  outside_temp integer,
  zone_water_temp integer,
  buffer_temp integer,
  room_thermostat_temp integer,
  compressor_freq integer,
  heat_energy_consumption integer,
  heat_energy_production integer,
  heat_request_temp integer,
  pump_power integer
);

CREATE INDEX readings_idx_date ON readings(time);
