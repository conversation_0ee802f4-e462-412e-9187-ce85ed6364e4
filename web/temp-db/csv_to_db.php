<?php declare(strict_types=1);

$file = fopen("/home/<USER>/tmp/temperatures.csv","r");
$conn = getConnection();

while(! feof($file)) {
    $dict = fgetcsv($file);
    if (! $dict) {
        continue;
    }

    [$year, $month, $day, $hour] = $dict;

    $hour = explode(":", $hour)[0];
    $temperature = $dict[9];

    printf("%s %s %s %s %s\n", $year, $month, $day, $hour, $temperature);

    insertRow($conn, [$year, $month, $day, $hour, $temperature]);

    // print_r($dict[9]);
}

fclose($file);

function insertRow(PDO $conn, array $row): void {

    $stmt = $conn->prepare(
        'INSERT INTO temperatures (year, month, day, hour, temp) 
             VALUES (:year, :month, :day, :hour, :temp)');

    $stmt->bindParam(':year', $row[0]);
    $stmt->bindParam(':month', $row[1]);
    $stmt->bindParam(':day', $row[2]);
    $stmt->bindParam(':hour', $row[3]);
    $stmt->bindParam(':temp', $row[4]);

    $stmt->execute();
}

function getConnection(): PDO {
    $conn = new PDO('sqlite:/home/<USER>/tmp/temperature.sqlite');
    $conn->setAttribute(PDO::ATTR_ERRMODE,
        PDO::ERRMODE_EXCEPTION);

    return $conn;
}
