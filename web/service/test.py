#!/usr/bin/env python3

import serial
import time

dev = "/dev/ttyACM0"
dev = "/dev/ttyUSB0"

ser = serial.Serial(
    port=dev,
    baudrate=9600,
    timeout=0.2,
    bytesize=serial.EIGHTBITS,
    parity=serial.PARITY_NONE,
    stopbits=serial.STOPBITS_ONE,
)

time.sleep(1)
ser.write(bytes([65]))
time.sleep(1)

# print(ser.inWaiting())

if ser.inWaiting():
    data = ser.read(ser.inWaiting())
    print(data)
