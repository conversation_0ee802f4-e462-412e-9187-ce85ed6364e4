#!/usr/bin/env python3

import glob
import time
import serial
import sqlite3
import RPi.GPIO as GPIO

from modules import mqtt_data as mqtt
from modules import riser as riser

DEVICE_INDICES = [0, 1, 2]
SLEEP_TIME = 1 * 60
INPUT_PIN_NO = 10


GPIO.setmode(GPIO.BOARD)  # Use physical pin numbering
GPIO.setup(INPUT_PIN_NO, GPIO.IN, pull_up_down=GPIO.PUD_DOWN)


def insert_readings(data):
    conn = sqlite3.connect('./readings.sqlite')

    query = '''insert into readings (
            time, 
            pi_from_buffer,
            pi_outside_temp,
            pi_from_pump,
            relay,
            Pump_Flow,
            Main_Inlet_Temp,
            Main_Outlet_Temp,
            Main_Target_Temp,
            Outside_Temp,
            Heat_Energy_Production,
            Heat_Energy_Consumption,
            Z1_Water_Temp,
            Buffer_Temp,
            Defrosting_State,
            Compressor_Freq,
            Operations_Counter,
            Z1_Heat_Request_Temp,
            Z1_Water_Target_Temp,
            Room_Thermostat_Temp,
            Internal_Heater_State) 
        values (STRFTIME('%s')''' + (', ?' * 20) + ')'

    conn.execute(query, data)

    conn.commit()

    conn.close()


broker = mqtt.get_broker()


def main_loop():
    while True:
        try:
            riser.correct_temp_if_needed()
            insert_readings(get_all_readings())
            # print(get_all_readings())
            # log(format_line(read_all_temperatures(), read_relay_state()))
            time.sleep(SLEEP_TIME)
        except KeyboardInterrupt:
            broker.loop_stop()
            break


def get_all_readings():
    # relay_state = read_relay_state()
    # relay_state = 1 if riser.is_high() else 0
    relay_state = read_pump_speed() / 100
    # return [relay_state] + mqtt.get_readings()
    return read_all_temperatures() + [relay_state] + mqtt.get_readings()


def read_relay_state() -> int:
    return 1 if GPIO.input(INPUT_PIN_NO) == GPIO.HIGH else 0


def read_all_temperatures() -> list:
    return list(map(lambda idx: read_temp(idx), DEVICE_INDICES))


def log(line: str):
    # print(line)
    # return
    filename = "data_" + time.strftime("%y-%m-%d") + ".txt"
    with open(filename, "a") as fh:
        fh.write(line + "\n")


def read_temp_test(device_index: int) -> int:
    return device_index


def read_temp(device_index: int) -> float:

    device = glob.glob("/sys/bus/w1/devices/" + "28*")[device_index] + "/w1_slave"

    lines = ['']

    while lines[0].strip()[-3:] != "YES":
        time.sleep(0.2)
        with open(device, "r") as f:
            lines = f.readlines()

    equals_pos = lines[1].find("t=")

    if equals_pos == -1:
        return 0

    temp_string = lines[1][equals_pos + 2:]

    return round(float(temp_string) / 1000.0, 1)


def read_pump_speed() -> int:
    try:
        with serial.Serial('/dev/ttyACM0', 9600, timeout=1) as ser:
            ser.reset_input_buffer()
            for n in range(1, 5):
                if ser.in_waiting > 0:
                    return int(ser.readline().decode('utf-8').rstrip())
                time.sleep(1)
    except:
        return 0

    return 0

if __name__ == "__main__":
    # print(get_all_readings())
    # print(format_line(read_all_temperatures()))
    main_loop()
