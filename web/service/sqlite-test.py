#!/usr/bin/python

from datetime import datetime
import sqlite3


def insert_readings(data):
    conn = sqlite3.connect('../temp-db/readings.sqlite')

    query = '''insert into readings (
                time, 
                ext_temp_1,
                ext_temp_2,
                ext_temp_3,
                heatpump_state,
                defrosting_state,
                internal_heater_state,
                valve_state,
                pump_flow,
                inlet_temp,
                outlet_temp,
                target_temp,
                outside_temp,
                zone_water_temp,
                buffer_temp,
                room_thermostat_temp,
                compressor_freq,
                heat_energy_consumption,
                heat_energy_production,
                heat_request_temp) 
            values (STRFTIME('%s')''' + (', ?' * 19) + ')'

    conn.execute(query, data)

    conn.commit()

    conn.close()


insert_readings([1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4])
