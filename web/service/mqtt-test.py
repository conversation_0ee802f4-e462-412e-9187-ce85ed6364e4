import paho.mqtt.client as mqtt
import time

TOPIC_PREFIX = "panasonic_heat_pump/main/";
TOPICS = ["Pump_Flow",
          "Main_Inlet_Temp",
          "Main_Outlet_Temp",
          "Main_Target_Temp",
          "Outside_Temp",
          "Heat_Energy_Production",
          "Heat_Energy_Consumption",
          "Z1_Water_Temp",
          "Buffer_Temp"]
DATA_DICT = {each: 0 for each in TOPICS}


def on_message(client, userdata, message):
    date_time = time.strftime("%y-%m-%d@%H:%M:%S")
    data = message.payload.decode("utf-8")
    topic = message.topic

    mes = "{} - {}/{}".format(date_time, topic, data)
    # print(mes)

    key = topic.replace(TOPIC_PREFIX, "")

    DATA_DICT[key] = data


def get_broker():
    client = mqtt.Client("My-client")
    client.connect("raspberrypi")
    client.loop_start()
    client.subscribe(list(map(lambda topic: (TOPIC_PREFIX + topic, 0), TOPICS)))
    client.on_message = on_message
    return client


broker = get_broker()


while True:
    try:
        # print(".")
        result = list(map(lambda topic: str(DATA_DICT[topic]), TOPICS))

        print(' '.join(result))

        time.sleep(30)
    except KeyboardInterrupt:
        broker.loop_stop()
        break

# client.loop_stop()
