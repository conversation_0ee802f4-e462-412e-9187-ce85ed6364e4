#!/usr/bin/python3

import glob
import time
import RPi.GPIO as GPIO

SLEEP_TIME = 2  # seconds
RELAY_PIN_NO = 36

GPIO.setmode(GPIO.BOARD)  # Use physical pin numbering
GPIO.setup(RELAY_PIN_NO, GPIO.OUT)
GPIO.output(RELAY_PIN_NO, GPIO.HIGH)

def main_loop():
    while True:
        try:
            temp = read_temp()

            write_to_file(str(temp))

            print(temp)

            if temp > 21:
                GPIO.output(RELAY_PIN_NO, GPIO.LOW)
            else:
                GPIO.output(RELAY_PIN_NO, GPIO.HIGH)

            time.sleep(SLEEP_TIME)
        except KeyboardInterrupt:
            GPIO.cleanup()
            break


def write_to_file(line: str):
    with open("temp-data.txt", "w") as fh:
        fh.write(line + "\n")


def read_temp() -> float:
    device = glob.glob("/sys/bus/w1/devices/" + "28*")[0] + "/w1_slave"

    with open(device, "r") as f:
        lines = f.readlines()

    while lines[0].strip()[-3:] != "YES":
        time.sleep(0.2)

    equals_pos = lines[1].find("t=")

    if equals_pos == -1:
        return 0

    temp_string = lines[1][equals_pos + 2 :]

    return round(float(temp_string) / 1000.0, 1)


if __name__ == "__main__":
    main_loop()
