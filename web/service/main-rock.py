#!/usr/bin/env python3

import time
import serial
import sqlite3
import sys

from modules import linux_mq_reader
from modules import temp_reader
from modules.reading import Reading
from modules.state import State
from modules.message_builder import build_message, is_valid_message
from modules.message_handler import handle_message

IS_DEBUG = False
TEMP_DEVICES = [
    (Reading.EXT_TEMP_1, "28-012211278b07"),
    (Reading.EXT_TEMP_2, "28-0122114f32a5"),
    (Reading.EXT_TEMP_3, "28-01221159ba3b"),
]

INSERT_EVERY = 600  # 60 sec. step is 0.1
WRITE_STATE_EVERY = 10  # 1 sec
INPUT_PIN_NO = 10
PORT = "/dev/ttyUSB0"
STATE_DIR = "/mnt/ramdisk"

state = State()

if len(sys.argv) == 2 and sys.argv[1] == "debug":
    IS_DEBUG = True
    print("Debug mode enabled")

arduinoSerial = serial.Serial(
    port=PORT,
    baudrate=9600,
    timeout=0.1,
    bytesize=serial.EIGHTBITS,
    parity=serial.PARITY_NONE,
    stopbits=serial.STOPBITS_ONE,
)


def insert_readings(readings: dict[Reading, int]):
    conn = sqlite3.connect("./readings.sqlite")

    keys = ", ".join([reading.key for reading, _ in readings.items()])
    placeholders = (", ?" * 20)

    query = """insert into readings (time, {}) 
               values (STRFTIME('%s') {})""".format(keys, placeholders)

    values = [value for _, value in readings.items()]

    conn.execute(query, values)
    conn.commit()
    conn.close()


def main_loop():
    count = 500
    while True:
        try:
            handle_messages_from_queue()

            read_from_arduino()

            if not IS_DEBUG and count % INSERT_EVERY == 0:
                state.update_readings(read_all_temperatures())
                insert_readings(state.readings)

            if count % WRITE_STATE_EVERY == 0:
                state.write_to_dir(STATE_DIR)

            count = count + 1

            time.sleep(0.1)

        except KeyboardInterrupt:
            break


def handle_messages_from_queue():
    msg = linux_mq_reader.get_message()
    if msg is None:
        return

    numbers = [int(x) for x in msg.split(";")]

    if IS_DEBUG:
        print("from web: " + str(numbers))

    for number in numbers:
        arduinoSerial.write(bytes([number]))


def read_from_arduino():
    if not arduinoSerial.inWaiting():
        return

    data = [int(x) for x in arduinoSerial.readall()]

    if IS_DEBUG:
        print("from arduino: " + str(data))

    if is_valid_message(data):
        msg = build_message(data)
        handle_message(msg, state)
    elif IS_DEBUG:
        print("invalid message")


def read_all_temperatures() -> dict[Reading, float]:
    return {
        reading: temp_reader.read_temp(dev_id) for (reading, dev_id) in TEMP_DEVICES
    }


def log(line: str) -> None:
    filename = "data_" + time.strftime("%y-%m-%d") + ".txt"
    with open(filename, "a") as fh:
        fh.write(line + "\n")


if __name__ == "__main__":
    main_loop()
