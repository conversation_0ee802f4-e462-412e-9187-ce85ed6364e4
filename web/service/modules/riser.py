import subprocess

from modules import mqtt_data as mqtt

IS_HIGH = True


def correct_temp_if_needed() -> None:
    # mqtt.DATA_DICT["Buffer_Temp"] = 20
    # mqtt.DATA_DICT["Main_Target_Temp"] = 23

    if has_reached_threshold() and is_running() and is_low():
        rise_temp()
    elif is_stopped() and is_high():
        lower_temp()


def has_reached_threshold() -> bool:
    return mqtt.get_buffer_temp() >= mqtt.get_target_temp() - 2


def is_running() -> bool:
    return mqtt.is_running()


def is_stopped() -> bool:
    return not is_running()


def rise_temp() -> None:
    global IS_HIGH
    IS_HIGH = True
    adjust_temp(3)
    print('rising ...')


def lower_temp() -> None:
    global IS_HIGH
    IS_HIGH = False
    adjust_temp(0)
    print('lowering ...')


def adjust_temp(direction: int):
    command = "mosquitto_pub -d -t panasonic_heat_pump/commands/SetZ1HeatRequestTemperature -m"

    output = subprocess.check_output(command.split() + [str(direction)])

    print(output.decode("utf-8"))


def is_low() -> bool:
    return not IS_HIGH


def is_high() -> None:
    return IS_HIGH

