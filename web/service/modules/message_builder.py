from modules.message import Message
from modules.message_type import MessageType

def build_message(data: list[int]):
    return Message(MessageType(data[2]), data[3:])

def is_valid_message(data: list[int]):
    if len(data) < 2 or data[0] != 0xAB or len(data) != data[1]:
        return False

    return is_valid_message_type(data[2])

def is_valid_message_type(value: int):
    return any(value == item.value for item in MessageType.__members__.values())

