import re

def read_temp(device_id: int) -> float:

    path = f'/sys/bus/w1/devices/{device_id}/w1_slave'

    with open(path, "r") as f:
        output = "\n".join(f.readlines())

    if is_success(output):
        return extract_temp(output)
    else:
        return 0


def is_success(output: str) -> bool:
    return re.search("crc=[0-9a-f]{2} YES", output) is not None

def extract_temp(output: str) -> float:
    r = re.search("t=(-?\\d+)$", output)
    if r is None:
        return 0

    return round(float(r.group(1)) / 1000.0, 1)
