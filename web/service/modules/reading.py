from enum import Enum, auto


class Reading(Enum):
    EXT_TEMP_1 = auto()
    EXT_TEMP_2 = auto()
    EXT_TEMP_3 = auto()
    HEATPUMP_STATE = auto()
    DEFROSTING_STATE = auto()
    INTERNAL_HEATER_STATE = auto()
    VALVE_STATE = auto()
    PUMP_FLOW = auto()
    INLET_TEMP = auto()
    OUTLET_TEMP = auto()
    TARGET_TEMP = auto()
    OUTSIDE_TEMP = auto()
    ZONE_WATER_TEMP = auto()
    BUFFER_TEMP = auto()
    ROOM_THERMOSTAT_TEMP = auto()
    COMPRESSOR_FREQ = auto()
    HEAT_ENERGY_CONSUMPTION = auto()
    HEAT_ENERGY_PRODUCTION = auto()
    HEAT_REQUEST_TEMP = auto()
    PUMP_POWER = auto()

    def __init__(self, ordinal):
        self.ordinal = ordinal
        self.key = self.name.lower()
