import time

from modules.message import Message
from modules.message_type import MessageType
from modules.state import State
from modules.reading import Reading


def handle_message(message: Message, state: State) -> None:
    match message:
        case Message(type=MessageType.TEST, data=data):
            state.test = data[0]
        case Message(type=MessageType.SET_POWER, data=data):
            state.power = data[0]
        case Message(type=MessageType.ENABLE_VALVE, data=data):
            state.valve_state = data[0]
        case Message(type=MessageType.CTRL_READINGS, data=data):
            state.debug = data[0]
            state.valve_enabled = data[1]
            state.valve_direction = data[2]
            state.duty_cycle_value = data[3]
        case Message(type=MessageType.PUMP_READINGS, data=data):
            state.update_readings(decode_arduino_data(data))
        case _:
            print("No handler for this message type")

    if message.type == MessageType.PUMP_READINGS:
        return

    line = format_log_line(message)

    state.log(line)

    with open("messages.txt", "a") as file:
        file.write(line)


def decode_arduino_data(data: list[int]) -> dict[Reading, float]:
    return {
        Reading.HEATPUMP_STATE: data[0],
        Reading.DEFROSTING_STATE: data[1],
        Reading.INTERNAL_HEATER_STATE: data[2],
        Reading.VALVE_STATE: data[3],
        Reading.PUMP_FLOW: data[4],
        Reading.INLET_TEMP: data[5] - 128,
        Reading.OUTLET_TEMP: data[6] - 128,
        Reading.TARGET_TEMP: data[7] - 128,
        Reading.OUTSIDE_TEMP: data[8] - 128,
        Reading.ZONE_WATER_TEMP: data[9] - 128,
        Reading.BUFFER_TEMP: data[10] - 128,
        Reading.ROOM_THERMOSTAT_TEMP: data[11] - 128,
        Reading.COMPRESSOR_FREQ: data[12],
        Reading.HEAT_ENERGY_CONSUMPTION: data[13] * 200,
        Reading.HEAT_ENERGY_PRODUCTION: data[14] * 200,
        Reading.HEAT_REQUEST_TEMP: data[15] - 128,
        Reading.PUMP_POWER: data[16],
    }


def format_log_line(msg: Message) -> str:
    date_time = time.strftime("%y-%m-%d %H:%M")

    return "{} {} {}\n".format(date_time, msg.type, msg.data[0])
