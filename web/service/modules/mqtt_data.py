import paho.mqtt.client as mqtt

HOST = "localhost"
TOPIC_PREFIX = "panasonic_heat_pump/main/"
TOPICS = ["Pump_Flow",
          "Main_Inlet_Temp",
          "Main_Outlet_Temp",
          "Main_Target_Temp",
          "Outside_Temp",
          "Heat_Energy_Production",
          "Heat_Energy_Consumption",
          "Z1_Water_Temp",
          "Buffer_Temp",
          "Defrosting_State",
          "Compressor_Freq",
          "Operations_Counter",
          "Z1_Heat_Request_Temp",
          "Z1_Water_Target_Temp",
          "Room_Thermostat_Temp",
          "Internal_Heater_State"]

DATA_DICT = {each: 0 for each in TOPICS}

def is_running() -> bool:
    return DATA_DICT["Compressor_Freq"] > 0


def get_target_temp() -> int:
    return DATA_DICT["Main_Target_Temp"]


def get_buffer_temp() -> int:
    return DATA_DICT["Buffer_Temp"]


def get_readings() -> list:
    return list(map(lambda topic: DATA_DICT[topic], TOPICS))


def on_message(client, userdata, message):
    data = message.payload.decode("utf-8")
    topic = message.topic
    key = topic.replace(TOPIC_PREFIX, "")
    DATA_DICT[key] = int(float(data))


def get_broker():
    client = mqtt.Client("My-client")
    client.connect(HOST)
    client.loop_start()
    client.subscribe(list(map(lambda topic: (TOPIC_PREFIX + topic, 0), TOPICS)))
    client.on_message = on_message
    return client
