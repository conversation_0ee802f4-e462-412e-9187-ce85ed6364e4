import os
import json

from modules.reading import Reading

LOCK_FILE = "heatpump.lock"
STATE_FILE = "heatpump.state.json"

class State:
    def __init__(self):
        self.debug = 0
        self.valve_enabled = 0
        self.valve_direction = 0
        self.duty_cycle_value = 0
        self.power = 0
        self.messages = []
        self.readings = {reading: 0 for reading in Reading}

    def __str__(self):
        return self.as_json()

    def update_readings(self, readings: dict[Reading, float]) -> None:
        self.readings = {**self.readings, **readings}

    def as_json(self):
        readings = {reading.key: value for reading, value in self.readings.items()}

        ctrl_readings = {
            "debug": self.debug,
            "power": self.power,
            "valve_enabled": self.valve_enabled,
            "valve_direction": self.valve_direction,
            "duty_cycle_value": self.duty_cycle_value
        }

        data = {
            "messages": self.messages,
            "readings": readings,
            "ctrl-readings": ctrl_readings,
        }

        return json.dumps(data)

    def log(self, message):
        self.messages.insert(0, message)
        self.messages = self.messages[:20]

    def write_to_dir(self, directory) -> None:
        lock_path = f"{directory}/{LOCK_FILE}"
        state_path = f"{directory}/{STATE_FILE}"

        with open(lock_path, "w") as fl:
            fl.write(str(os.getpid()))

            with open(state_path, "w") as fs:
                fs.write(self.as_json())
                fs.write("\n")

        os.remove(lock_path)
