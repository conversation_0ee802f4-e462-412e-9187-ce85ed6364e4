#!/usr/bin/env python3

import glob
import time
import RPi.GPIO as GPIO
import sqlite3

SLEEP_TIME = 60

GPIO.setmode(GPIO.BOARD)  # Use physical pin numbering

def insert_readings(data):
    conn = sqlite3.connect("./readings.sqlite")

    query = (
        """insert into readings (
            time, 
            ext_temp_1,
            ext_temp_2,
            ext_temp_3,
            heatpump_state,
            defrosting_state,
            internal_heater_state,
            valve_state,
            pump_flow,
            inlet_temp,
            outlet_temp,
            target_temp,
            outside_temp,
            zone_water_temp,
            buffer_temp,
            room_thermostat_temp,
            compressor_freq,
            heat_energy_consumption,
            heat_energy_production,
            heat_request_temp) 
        values (STRFTIME('%s')"""
        + (", ?" * 19)
        + ")"
    )

    conn.execute(query, data)

    conn.commit()

    conn.close()


def main_loop():
    while True:
        try:
            # log(format_line(read_all_temperatures()))
            readings = [0.0] * 19
            readings[0] = read_temp(0)

            insert_readings(readings)
            time.sleep(SLEEP_TIME)
        except KeyboardInterrupt:
            break


def format_line(temperatures: str):
    date_time = time.strftime("%y-%m-%d@%H:%M:%S")

    return "{} - {}".format(date_time, temperatures)


def log(line: str):
    # print(line)
    # return
    filename = "data_" + time.strftime("%y-%m-%d") + ".txt"
    with open(filename, "a") as fh:
        fh.write(line + "\n")


def read_temp_test(device_index: int) -> int:
    return device_index


def read_temp(device_index: int) -> float:
    device = glob.glob("/sys/bus/w1/devices/" + "28*")[device_index] + "/w1_slave"

    with open(device, "r") as f:
        lines = f.readlines()

    while lines[0].strip()[-3:] != "YES":
        time.sleep(0.2)

    equals_pos = lines[1].find("t=")

    if equals_pos == -1:
        return 0

    temp_string = lines[1][equals_pos + 2 :]

    return round(float(temp_string) / 1000.0, 1)


if __name__ == "__main__":
    main_loop()
    # print(format_line(read_all_temperatures()))
