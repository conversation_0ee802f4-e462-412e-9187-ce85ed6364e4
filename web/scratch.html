<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Custom Image Radio Buttons Without <input> Elements</title>
    <style>
        body {
            font-family: sans-serif;
        }

        .radio {
            cursor: pointer;
        }

        .radio img {
            width: 20px;
            height: 20px;
        }
    </style>

</head>
<body>

<label data-name="r1" data-value="off" class="radio">
    <img src="img/radio-off.svg">on
</label>
<label data-name="r1" data-value="on" class="radio">
    <img src="img/radio-off.svg">off
</label>

<script>

    class CustomRadio {
        constructor(name, initialValue, onChangeCallback) {
            this.value = initialValue;
            this.name = name;
            this.onChangeCallback = onChangeCallback;
            this.options = document.querySelectorAll(`[data-name="${this.name}"]`);

            for (const each of this.options) {
                each.addEventListener('click', this.requestStateChange.bind(this));
            }

            this.paint();
        }

        setValue(newValue) {
            this.value = newValue;
            this.paint();
        }

        requestStateChange(event) {
            let selected = event.target;
            if (selected.tagName === 'IMG') {
                selected = selected.parentNode;
            }

            let newValue = selected.getAttribute('data-value');
            console.log("Set: ", newValue);

            this.onChangeCallback(this, newValue);
        }

        paint() {
            for (const each of this.options) {
                if (each.getAttribute('data-value') === this.value) {
                    each.classList.add('selected');
                    each.firstElementChild.src = 'img/radio-on.svg';
                } else {
                    each.classList.remove('selected');
                    each.firstElementChild.src = 'img/radio-off.svg';
                }
            }
        }
    }

    const onChange = (radio, newValue) => {
        setTimeout(() => {
            radio.setValue(newValue);

            console.log("new value is: ", radio.value);

        }, 500);
    }

    new CustomRadio("r1", null, onChange);

</script>

</body>
</html>
