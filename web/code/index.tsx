import React from 'react'
import ReactD<PERSON> from 'react-dom'

import AppComp from './AppComp.tsx';
import { HashRouter } from "react-router-dom";
import { AppProvider } from './context/AppContext.tsx';
import ErrorBoundary from './components/ErrorBoundary.tsx';

ReactDOM.createRoot(document.getElementById("main")).render(
    <React.StrictMode>
        <ErrorBoundary>
            <AppProvider>
                <HashRouter basename='/'>
                    <AppComp />
                </HashRouter>
            </AppProvider>
        </ErrorBoundary>
    </React.StrictMode>);
