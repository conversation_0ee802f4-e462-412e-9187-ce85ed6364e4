export interface ReadingData {
    [key: string]: any;
}

export interface ControllerState {
    messages: string[];
    readings: { [key: string]: number };
    'ctrl-readings': {
        debug: number;
        power: number;
        valve_enabled: number;
        valve_direction: number;
        duty_cycle_value: number;
    };
}

export interface MessageLogEntry {
    timestamp: string;
    message: string;
}

export default class Dao {
    async fetchReadings(date: string | null): Promise<ReadingData[]> {
        const url = date ? `api.php?cmd=readings&date=${date}` : 'api.php?cmd=readings';
        return fetch(url).then(r => r.json());
    }

    async fetchLatestReadings(): Promise<ReadingData[]> {
        return fetch('api.php?cmd=fetch-latest-readings')
            .then(r => r.json());
    }

    async fetchLatestDates(): Promise<string[]> {
        return await fetch('api.php?cmd=latest-dates').then(r => r.json());
    }

    async fetchMessageLog(): Promise<MessageLogEntry[]> {
        return await fetch('api.php?cmd=fetch-message-log').then(r => r.json());
    }

    async fetchControllerState(): Promise<ControllerState> {
        return await fetch('api.php?cmd=fetch-ctrl-state').then(r => r.json());
    }

    async setTemperature(temp: number): Promise<any> {
        const url = `api.php?cmd=set-temperature&temp=${temp}`;
        return await fetch(url).then(r => r.json());
    }

    async testCommand(): Promise<any> {
        return await fetch('api.php?cmd=test').then(r => r.json());
    }

    async enableValve(enable: boolean): Promise<any> {
        const url = `api.php?cmd=valve-enable&enable=${enable}`;
        return await fetch(url).then(r => r.json());
    }

    async setPower(state: boolean): Promise<any> {
        const url = `api.php?cmd=set-power&state=${state}`;
        return await fetch(url).then(r => r.json());
    }
}
