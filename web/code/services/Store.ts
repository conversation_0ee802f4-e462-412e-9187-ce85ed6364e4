import { ReadingKey } from './Repository.ts';

const READING_IDS_KEY = 'reading-ids-key';

export function saveSelectedReadingIds(readingIds: ReadingKey[]): void {
    const json = JSON.stringify(readingIds);
    localStorage.setItem(READING_IDS_KEY, json);
}

export function getSelectedReadingIds(): ReadingKey[] {
    const json = localStorage.getItem(READING_IDS_KEY);
    return json ? JSON.parse(json) : [];
}
