import { formatDate, round } from '../utils/common.ts';

export type ReadingKey = 
    | 'time'
    | 'ext_temp_1'
    | 'ext_temp_2' 
    | 'ext_temp_3'
    | 'heatpump_state'
    | 'defrosting_state'
    | 'internal_heater_state'
    | 'valve_state'
    | 'pump_flow'
    | 'inlet_temp'
    | 'outlet_temp'
    | 'target_temp'
    | 'outside_temp'
    | 'zone_water_temp'
    | 'buffer_temp'
    | 'room_thermostat_temp'
    | 'compressor_freq'
    | 'heat_energy_consumption'
    | 'heat_energy_production'
    | 'heat_request_temp'
    | 'pump_power'
    | 'COP';

const readingToIndex: Array<[ReadingKey, number | null]> = [
    ['time', 0],
    ['ext_temp_1', 1],
    ['ext_temp_2', 2],
    ['ext_temp_3', 3],
    ['heatpump_state', 4],
    ['defrosting_state', 5],
    ['internal_heater_state', 6],
    ['valve_state', 7],
    ['pump_flow', 8],
    ['inlet_temp', 9],
    ['outlet_temp', 10],
    ['target_temp', 11],
    ['outside_temp', 12],
    ['zone_water_temp', 13],
    ['buffer_temp', 14],
    ['room_thermostat_temp', 15],
    ['compressor_freq', 16],
    ['heat_energy_consumption', 17],
    ['heat_energy_production', 18],
    ['heat_request_temp', 19],
    ['pump_power', 20],
    ['COP', null],
];

const readingToIndexMap = new Map(readingToIndex);

export default class Repository {
    private readings: any[][] = [];

    update(listOfLists: any[][]): Repository {
        this.readings = listOfLists;
        return this;
    }

    getSize(): number {
        return this.readings.length;
    }

    getReadings(readingKey: ReadingKey): any[] {
        const index = readingToIndexMap.get(readingKey);
        if (index === null || index === undefined) {
            return [];
        }
        return this.readings.map(readingList => readingList[index]);
    }

    getAllReadings(): ReadingSet[] {
        return this.readings.map(readingList => new ReadingSet(readingList));
    }

    getAllIds(): ReadingKey[] {
        return readingToIndex.map(each => each[0]).slice(1) as ReadingKey[];
    }
}

export class ReadingSet {
    private values: any[];

    constructor(values: any[]) {
        this.values = values;
    }

    getReading(readingKey: ReadingKey): number {
        const index = readingToIndexMap.get(readingKey);
        
        if (index === null || index === undefined) {
            return 0;
        }

        const reading = this.values[index];

        if (readingKey === 'heat_energy_production' || readingKey === 'heat_energy_consumption') {
            return reading / 1000;
        } else if (readingKey === 'COP') {
            const prod = this.getReading('heat_energy_production');
            const consumption = this.getReading('heat_energy_consumption');
            const compressor = this.getReading('compressor_freq');
            const defrosting = this.getReading('defrosting_state');

            return compressor === 0 || defrosting === 1
                ? 0
                : round(prod / consumption);
        }

        return reading;
    }

    getConsumption(): number {
        return this.getReading('heat_energy_consumption');
    }

    getProduction(): number {
        return this.getReading('heat_energy_production');
    }

    isRunning(): boolean {
        return parseInt(this.getReading('compressor_freq').toString()) > 0;
    }

    getOutsideTemp(): number {
        return parseInt(this.getReading('outside_temp').toString());
    }

    getRequestTemp(): number {
        return parseInt(this.getReading('heat_request_temp').toString());
    }

    getLabel(): string {
        return formatDate(this.values[0], 'hm');
    }
}
