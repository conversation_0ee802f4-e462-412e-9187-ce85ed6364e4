import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import Repository from '../services/Repository.ts';
import Dao, {ReadingData} from '../services/Dao.ts';

// State interface
export interface AppState {
    repository: Repository;
    dao: Dao;
    selectedDate: string | null;
    isLoading: boolean;
    error: string | null;
    availableDates: string[];
}

// Action types
export type AppAction =
    | { type: 'SET_LOADING'; payload: boolean }
    | { type: 'SET_ERROR'; payload: string | null }
    | { type: 'SET_SELECTED_DATE'; payload: string | null }
    | { type: 'SET_AVAILABLE_DATES'; payload: string[] }
    | { type: 'UPDATE_REPOSITORY_DATA'; payload: ReadingData[] }
    | { type: 'REFRESH_DATA' }
    | { type: 'FORCE_REFRESH' };

// Initial state
const initialState: AppState = {
    repository: new Repository(),
    dao: new Dao(),
    selectedDate: null,
    isLoading: false,
    error: null,
    availableDates: [],
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, isLoading: action.payload };
        case 'SET_ERROR':
            return { ...state, error: action.payload };
        case 'SET_SELECTED_DATE':
            return { ...state, selectedDate: action.payload };
        case 'SET_AVAILABLE_DATES':
            return { ...state, availableDates: action.payload };
        case 'UPDATE_REPOSITORY_DATA':
            const newRepository = new Repository();
            newRepository.update(action.payload);
            return { ...state, repository: newRepository };
        case 'FORCE_REFRESH':
            // This action can be used to trigger re-renders or force data refresh
            return { ...state };
        default:
            return state;
    }
}

// Context
const AppContext = createContext<{
    state: AppState;
    dispatch: React.Dispatch<AppAction>;
} | undefined>(undefined);

// Provider component
export function AppProvider({ children }: { children: ReactNode }) {
    const [state, dispatch] = useReducer(appReducer, initialState);

    return (
        <AppContext.Provider value={{ state, dispatch }}>
            {children}
        </AppContext.Provider>
    );
}

// Custom hook to use the context
export function useAppContext() {
    const context = useContext(AppContext);
    if (context === undefined) {
        throw new Error('useAppContext must be used within an AppProvider');
    }
    return context;
}

// Custom hooks for common operations
export function useRepository() {
    const { state } = useAppContext();
    return state.repository;
}

export function useDao() {
    const { state } = useAppContext();
    return state.dao;
}

export function useAppState() {
    const { state } = useAppContext();
    return {
        selectedDate: state.selectedDate,
        isLoading: state.isLoading,
        error: state.error,
        availableDates: state.availableDates,
    };
}
