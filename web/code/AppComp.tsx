import React from 'react';
import { Redirect, Route, Switch } from "react-router-dom";
import { useDataLoader } from './hooks/useDataLoader.ts';
import { useAppState, useRepository } from './context/AppContext.tsx';
import { useRefresh } from './hooks/useRefresh.ts';
import { isDevelopment } from './utils/common.ts';
import Navigation from './components/Navigation.tsx';
import GraphPage from './components/GraphPage.tsx';
import DetailsPage from './components/DetailsPage.tsx';
import CommandsPage from './components/CommandsPage.tsx';

const AppComp = () => {
    const { isLoading, error, availableDates, selectedDate } = useDataLoader();
    const repository = useRepository();

    // Initialize refresh event handling
    useRefresh();

    return (
        <div>
            <header>
                <Navigation />
                {error && (
                    <div className="alert alert-danger" role="alert">
                        Error: {error}
                    </div>
                )}
            </header>

            <div className="container">

                <Switch>
                    <Route path="/graph">
                        <GraphPage />
                    </Route>
                    <Route path="/details">
                        <DetailsPage />
                    </Route>
                    <Route path="/commands">
                        <CommandsPage />
                    </Route>
                    <Route path="/">
                        <Redirect to='/graph' />
                    </Route>
                </Switch>
            </div>
        </div>
    );
}

export default AppComp;