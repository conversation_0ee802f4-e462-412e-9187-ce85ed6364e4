import { useEffect } from 'react';
import { useAppContext } from '../context/AppContext.tsx';

export function useDataLoader() {
    const { state, dispatch } = useAppContext();
    const { dao, selectedDate } = state;

    useEffect(() => {
        initializeData();
    }, []);

    useEffect(() => {
        if (selectedDate !== null) {
            loadReadings(selectedDate);
        }
    }, [selectedDate]);

    const initializeData = async () => {
        try {
            dispatch({ type: 'SET_LOADING', payload: true });

            const dates = await dao.fetchLatestDates();
            dispatch({ type: 'SET_AVAILABLE_DATES', payload: dates });

            const latestDate = dates[0] ?? null;
            dispatch({ type: 'SET_SELECTED_DATE', payload: latestDate });
            dispatch({ type: 'SET_ERROR', payload: null });
        } catch (error) {
            console.error('Failed to initialize data:', error);
            dispatch({ type: 'SET_ERROR', payload: 'Failed to load initial data' });
        } finally {
            dispatch({ type: 'SET_LOADING', payload: false });
        }
    };

    const loadReadings = async (date: string | null) => {
        try {
            dispatch({ type: 'SET_LOADING', payload: true });

            const readings = await dao.fetchReadings(date);
            dispatch({ type: 'UPDATE_REPOSITORY_DATA', payload: readings });

            dispatch({ type: 'SET_ERROR', payload: null });
        } catch (error) {
            console.error('Failed to load readings:', error);
            dispatch({ type: 'SET_ERROR', payload: 'Failed to load readings' });
        } finally {
            dispatch({ type: 'SET_LOADING', payload: false });
        }
    };

    const changeSelectedDate = (date: string | null) => {
        dispatch({ type: 'SET_SELECTED_DATE', payload: date });
    };

    const refreshData = () => {
        if (selectedDate !== null) {
            loadReadings(selectedDate);
        } else {
            initializeData();
        }
    };

    return {
        changeSelectedDate,
        refreshData,
        isLoading: state.isLoading,
        error: state.error,
        availableDates: state.availableDates,
        selectedDate: state.selectedDate,
    };
}
