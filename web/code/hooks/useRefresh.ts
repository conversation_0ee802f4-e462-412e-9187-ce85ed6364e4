import { useEffect } from 'react';
import { useAppContext } from '../context/AppContext.tsx';

/**
 * Custom hook that mimics the original application's refresh event system
 * This allows components to trigger and listen for refresh events
 */
export function useRefresh() {
    const { dispatch } = useAppContext();

    // Listen for custom refresh events (similar to original app)
    useEffect(() => {
        const handleRefresh = () => {
            dispatch({ type: 'FORCE_REFRESH' });
        };

        // Listen for the custom 'refresh' event that the original app used
        window.addEventListener('refresh', handleRefresh);

        return () => {
            window.removeEventListener('refresh', handleRefresh);
        };
    }, [dispatch]);

    // Function to trigger a refresh event (similar to original app)
    const triggerRefresh = () => {
        window.dispatchEvent(new Event('refresh'));
    };

    return {
        triggerRefresh
    };
}

/**
 * Hook for components that need to trigger refresh when data changes
 * (similar to how the original app used window.dispatchEvent(new Event('refresh')))
 */
export function useRefreshTrigger() {
    const triggerRefresh = () => {
        window.dispatchEvent(new Event('refresh'));
    };

    return triggerRefresh;
}
