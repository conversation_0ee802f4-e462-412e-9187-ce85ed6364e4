import { sum, round } from './common.ts';
import { ReadingSet } from '../services/Repository.ts';

export function getTotalConsumption(readingsList: ReadingSet[]): string {
    const totalSum = readingsList
        .map(readings => readings.getConsumption())
        .reduce((prev, current) => prev + current, 0);

    return (totalSum / 60).toFixed(2);
}

export function getCopByTemp(readingsList: ReadingSet[]): Array<[number, number]> {
    const result: Array<[number, number]> = [];
    
    for (const readingsByTemp of getHourlyEnergyByTemp(readingsList)) {
        const [temp, readings] = readingsByTemp;
        const consumed = sum(readings.map(r => r.getConsumption()));
        const produced = sum(readings.map(r => r.getProduction()));
        result.push([temp, round(produced / consumed)]);
    }

    return result;
}

export function getHourlyEnergyByTemp(readingsList: ReadingSet[]): Array<[number, ReadingSet[]]> {
    const readingsByTemp = new Map<number, ReadingSet[]>();

    // Filter to only running readings
    const runningReadings = readingsList.filter(readings => readings.isRunning());

    for (const readings of runningReadings) {
        const temp = readings.getOutsideTemp();
        if (!readingsByTemp.has(temp)) {
            readingsByTemp.set(temp, []);
        }

        const list = readingsByTemp.get(temp)!;
        list.push(readings);
    }

    const results: Array<[number, ReadingSet[]]> = [];
    for (const [temp, readings] of readingsByTemp.entries()) {
        if (readings.length >= 60) { // at least hours worth of readings
            results.push([temp, readings]);
        }
    }

    return results.sort((a, b) => a[0] - b[0]);
}

export function getHourlyConsumptionByTemp(readingsList: ReadingSet[]): Array<[number, number]> {
    const result: Array<[number, number]> = [];
    
    for (const readingsByTemp of getHourlyEnergyByTemp(readingsList)) {
        const [temp, readings] = readingsByTemp;
        const consumed = sum(readings.map(r => r.getConsumption()));
        result.push([temp, round(consumed / readings.length)]);
    }

    return result;
}

export function getAverageRuntime(readingsList: ReadingSet[]): Array<[number, number]> {
    if (readingsList.length === 0) {
        return [];
    }

    const switchedOn = (prev: boolean, curr: boolean) => prev !== curr && curr === true;
    const switchedOff = (prev: boolean, curr: boolean) => prev !== curr && curr === false;

    let previousReading = readingsList[0].isRunning();
    let minutes = 0;
    let isRunning = true;
    const counts: number[] = [];

    for (const readings of readingsList) {
        const currentReading = readings.isRunning();

        if (switchedOff(previousReading, currentReading)) {
            isRunning = false;
            minutes = 0;
        } else if (switchedOn(previousReading, currentReading)) {
            if (minutes) {
                counts.push(minutes);
            }
        }

        previousReading = currentReading;

        if (!isRunning) {
            minutes++;
        }
    }

    return [];
}
