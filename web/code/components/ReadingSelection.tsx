import React from 'react';
import { ReadingKey } from '../services/Repository.ts';

interface ReadingSelectionProps {
    isVisible: boolean;
    allReadingIds: ReadingKey[];
    selectedReadingIds: ReadingKey[];
    onSelectionChange: (selectedIds: <PERSON><PERSON>ey[]) => void;
}

const ReadingSelection: React.FC<ReadingSelectionProps> = ({
    isVisible,
    allReadingIds,
    selectedReadingIds,
    onSelectionChange
}) => {
    const handleCheckboxChange = (readingId: ReadingKey, isChecked: boolean) => {
        let newSelection: ReadingKey[];
        
        if (isChecked) {
            // Add to selection if not already present
            newSelection = selectedReadingIds.includes(readingId) 
                ? selectedReadingIds 
                : [...selectedReadingIds, readingId];
        } else {
            // Remove from selection
            newSelection = selectedReadingIds.filter(id => id !== readingId);
        }
        
        onSelectionChange(newSelection);
    };

    return (
        <div 
            id="reading-selection"
            className="reading-selection"
            style={{ display: isVisible ? 'block' : 'none' }}
        >
            {allReadingIds.map(readingId => (
                <label key={readingId} className="reading-checkbox">
                    <input
                        type="checkbox"
                        value={readingId}
                        checked={selectedReadingIds.includes(readingId)}
                        onChange={(e) => handleCheckboxChange(readingId, e.target.checked)}
                    />
                    {readingId}
                    <br />
                </label>
            ))}
        </div>
    );
};

export default ReadingSelection;
