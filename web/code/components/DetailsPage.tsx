import React from 'react';
import { useRepository } from '../context/AppContext.tsx';
import { getTotalConsumption, getHourlyConsumptionByTemp, getCopByTemp, getAverageRuntime } from '../utils/calculator.ts';

const DetailsPage: React.FC = () => {
    const repository = useRepository();
    const readings = repository.getAllReadings();

    const totalConsumption = getTotalConsumption(readings);
    const hourlyConsumptionByTemp = getHourlyConsumptionByTemp(readings);
    const copByTemp = getCopByTemp(readings);
    const averageRuntime = getAverageRuntime(readings);

    return (
        <div id="details-page" className="details-page">
            <div className="detail-section">
                <h4>Day consumption</h4>
                <span id="total-consumption">{totalConsumption}</span>
            </div>

            <div className="detail-section">
                <h4>Hourly consumption by temp</h4>
                <div id="hourly-consumption-by-temp">
                    {hourlyConsumptionByTemp.map(([temp, consumption], index) => (
                        <div key={index}>
                            {temp}: {consumption}
                        </div>
                    ))}
                </div>
            </div>

            <div className="detail-section">
                <h4>COP by temp</h4>
                <div id="cop-by-temp">
                    {copByTemp.map(([temp, cop], index) => (
                        <div key={index}>
                            {temp}: {cop}
                        </div>
                    ))}
                </div>
            </div>

            {/* Average runtime section - currently not implemented in original */}
            {averageRuntime.length > 0 && (
                <div className="detail-section">
                    <h4>Average runtime by temp</h4>
                    <div id="average-runtime-by-temp">
                        {averageRuntime.map(([temp, runtime], index) => (
                            <div key={index}>
                                {temp}: {runtime}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default DetailsPage;
