import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useDataLoader } from '../hooks/useDataLoader.ts';
import LoadingSpinner from './LoadingSpinner.tsx';

interface NavigationProps {
    className?: string;
}

const Navigation: React.FC<NavigationProps> = ({ className = '' }) => {
    const { availableDates, selectedDate, changeSelectedDate, isLoading } = useDataLoader();
    const location = useLocation();

    const handleDateChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const newDate = event.target.value;
        changeSelectedDate(newDate);
    };

    const isActiveRoute = (path: string): boolean => {
        return location.pathname === path;
    };

    const getLinkClassName = (path: string): string => {
        return isActiveRoute(path) ? 'nav-link active' : 'nav-link';
    };

    return (
        <nav className={`navbar ${className}`}>
            <div className="nav-content">
                {/* File/Date Selector */}
                <div className="file-selector">
                    <select 
                        id="fileSelect"
                        value={selectedDate || ''}
                        onChange={handleDateChange}
                        disabled={isLoading || availableDates.length === 0}
                        className="form-select"
                    >
                        {availableDates.length === 0 ? (
                            <option value="">Loading dates...</option>
                        ) : (
                            availableDates.map(date => (
                                <option key={date} value={date}>
                                    {date}
                                </option>
                            ))
                        )}
                    </select>
                </div>

                {/* Navigation Links */}
                <div className="nav-links">
                    <Link 
                        to="/graph" 
                        className={getLinkClassName('/graph')}
                    >
                        Graph
                    </Link>
                    <span className="nav-separator">|</span>
                    <Link 
                        to="/details" 
                        className={getLinkClassName('/details')}
                    >
                        Details
                    </Link>
                    <span className="nav-separator">|</span>
                    <Link 
                        to="/commands" 
                        className={getLinkClassName('/commands')}
                    >
                        Commands
                    </Link>
                </div>

                {/* Loading indicator */}
                {isLoading && (
                    <LoadingSpinner size="small" text="Loading data..." />
                )}
            </div>
        </nav>
    );
};

export default Navigation;
