import React from 'react';

interface FileSelectorProps {
    availableDates: string[];
    selectedDate: string | null;
    onDateChange: (date: string) => void;
    isLoading?: boolean;
    className?: string;
}

const FileSelector: React.FC<FileSelectorProps> = ({
    availableDates,
    selectedDate,
    onDateChange,
    isLoading = false,
    className = ''
}) => {
    const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const newDate = event.target.value;
        if (newDate) {
            onDateChange(newDate);
        }
    };

    return (
        <div className={`file-selector ${className}`}>
            <select 
                id="fileSelect"
                value={selectedDate || ''}
                onChange={handleChange}
                disabled={isLoading || availableDates.length === 0}
                className="form-select"
            >
                {availableDates.length === 0 ? (
                    <option value="">
                        {isLoading ? 'Loading dates...' : 'No dates available'}
                    </option>
                ) : (
                    availableDates.map(date => (
                        <option key={date} value={date}>
                            {date}
                        </option>
                    ))
                )}
            </select>
        </div>
    );
};

export default FileSelector;
