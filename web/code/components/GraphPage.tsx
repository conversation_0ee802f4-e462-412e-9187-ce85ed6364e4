import React, { useState, useEffect, useRef } from 'react';
import { useRepository } from '../context/AppContext.tsx';
import { useRefreshTrigger } from '../hooks/useRefresh.ts';
import { getSelectedReadingIds, saveSelectedReadingIds } from '../services/Store.ts';
import { ReadingKey } from '../services/Repository.ts';
import Chart from './Chart.tsx';
import ReadingSelection from './ReadingSelection.tsx';

const PAGE_SIZE = 200;

const GraphPage: React.FC = () => {
    const repository = useRepository();
    const triggerRefresh = useRefreshTrigger();
    const [pageStart, setPageStart] = useState(0);
    const [isReadingSelectionVisible, setIsReadingSelectionVisible] = useState(false);
    const [selectedReadingIds, setSelectedReadingIds] = useState<ReadingKey[]>([]);

    // Initialize page position and selected readings
    useEffect(() => {
        const startPos = Math.max(0, repository.getSize() - PAGE_SIZE);
        setPageStart(startPos);
        setSelectedReadingIds(getSelectedReadingIds());
    }, [repository]);

    // Get current page of readings
    const getCurrentReadings = () => {
        return repository.getAllReadings().slice(pageStart, pageStart + PAGE_SIZE);
    };

    // Handle slider change
    const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newPageStart = parseInt(event.target.value);
        setPageStart(newPageStart);
    };

    // Handle reading selection change
    const handleReadingSelectionChange = (readingIds: ReadingKey[]) => {
        setSelectedReadingIds(readingIds);
        saveSelectedReadingIds(readingIds);
        triggerRefresh();
    };

    // Toggle reading selection visibility
    const toggleReadingSelection = () => {
        setIsReadingSelectionVisible(!isReadingSelectionVisible);
    };

    // Calculate slider properties
    const maxSliderValue = Math.max(0, repository.getSize() - PAGE_SIZE);
    const sliderSize = repository.getSize() > 0 ? (PAGE_SIZE / repository.getSize()) * 100 : 1;

    return (
        <div id="main-page" className="graph-page">
            {/* Reading Selection Panel */}
            <ReadingSelection
                isVisible={isReadingSelectionVisible}
                allReadingIds={repository.getAllIds()}
                selectedReadingIds={selectedReadingIds}
                onSelectionChange={handleReadingSelectionChange}
            />

            {/* Reading Selection Toggle */}
            <div 
                id="reading-selection-toggle"
                className="reading-selection-toggle"
                onClick={toggleReadingSelection}
            >
                Show/hide
            </div>

            {/* Chart Container */}
            <div id="canvas-container" className="canvas-container">
                <Chart 
                    readings={getCurrentReadings()}
                    selectedReadingIds={selectedReadingIds}
                />
            </div>

            {/* Controls */}
            <div id="controls" className="controls">
                <div className="slidecontainer">
                    <input
                        type="range"
                        min="0"
                        max={maxSliderValue}
                        value={pageStart}
                        className="slider"
                        id="slider"
                        onChange={handleSliderChange}
                        style={{
                            '--sliderSize': `${sliderSize}%`
                        } as React.CSSProperties}
                    />
                </div>
            </div>
        </div>
    );
};

export default GraphPage;
