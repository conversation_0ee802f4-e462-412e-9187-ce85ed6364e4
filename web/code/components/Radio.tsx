import React from 'react';

interface RadioOption {
    value: string;
    label: string;
}

interface RadioProps {
    name: string;
    value: string;
    options: RadioOption[];
    onChange: (value: string) => void;
    className?: string;
}

const Radio: React.FC<RadioProps> = ({
    name,
    value,
    options,
    onChange,
    className = ''
}) => {
    const handleOptionClick = (optionValue: string) => {
        if (optionValue !== value) {
            onChange(optionValue);
        }
    };

    return (
        <div className={`radio-group ${className}`}>
            {options.map((option) => (
                <label
                    key={option.value}
                    className={`radio ${value === option.value ? 'selected' : ''}`}
                    onClick={() => handleOptionClick(option.value)}
                >
                    <img
                        src={value === option.value ? 'img/radio-on.svg' : 'img/radio-off.svg'}
                        alt={value === option.value ? 'selected' : 'not selected'}
                    />
                    {option.label}
                </label>
            ))}
        </div>
    );
};

export default Radio;
