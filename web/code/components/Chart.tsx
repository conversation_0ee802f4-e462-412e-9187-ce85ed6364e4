import React, { useEffect, useRef } from 'react';
import { ReadingSet, ReadingKey } from '../services/Repository.ts';

// Declare Chart.js as a global variable (loaded via script tag)
declare global {
    interface Window {
        Chart: any;
    }
}

interface ChartProps {
    readings: ReadingSet[];
    selectedReadingIds: ReadingKey[];
}

interface ChartData {
    labels: string[];
    datasets: ChartDataset[];
}

interface ChartDataset {
    label: string;
    data: number[];
    borderColor: string;
}

const Chart: React.FC<ChartProps> = ({ readings, selectedReadingIds }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const chartRef = useRef<any>(null);

    // Initialize Chart.js
    useEffect(() => {
        if (!canvasRef.current || !window.Chart) {
            console.warn('Chart.js not loaded or canvas not available');
            return;
        }

        // Set Chart.js defaults
        window.Chart.defaults.elements.line.borderWidth = 1;
        window.Chart.defaults.elements.line.tension = 0.2;
        window.Chart.defaults.elements.point.radius = 2;

        // Create initial chart
        const initialData = createGraphData(readings, selectedReadingIds);
        chartRef.current = new window.Chart(canvasRef.current, {
            type: 'line',
            data: initialData
        });

        // Cleanup function
        return () => {
            if (chartRef.current) {
                chartRef.current.destroy();
                chartRef.current = null;
            }
        };
    }, []); // Only run once on mount

    // Update chart when data changes
    useEffect(() => {
        if (chartRef.current) {
            const newData = createGraphData(readings, selectedReadingIds);
            chartRef.current.data = newData;
            chartRef.current.update('none');
        }
    }, [readings, selectedReadingIds]);

    return (
        <canvas 
            ref={canvasRef}
            id="chartCanvas"
            style={{ width: '100%', height: '100%' }}
        />
    );
};

// Create chart data structure from readings
function createGraphData(readings: ReadingSet[], selectedIds: ReadingKey[]): ChartData {
    const colors = ['red', 'green', 'blue', 'black',
                    'BlueViolet', 'CadetBlue', 'Chocolate',
                    'DarkOliveGreen', 'DeepPink'];

    const datasets: ChartDataset[] = [];
    
    selectedIds.forEach((readingId, index) => {
        const color = colors[index % colors.length];
        const data = readings.map(reading => reading.getReading(readingId));
        
        datasets.push({
            label: readingId,
            data: data,
            borderColor: color
        });
    });

    const labels = readings.map(reading => reading.getLabel());

    return {
        labels: labels,
        datasets: datasets
    };
}

export default Chart;
