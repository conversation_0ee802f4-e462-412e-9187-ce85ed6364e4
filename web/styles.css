:root {
    --graph-width: 97vw;
}

body {
    font-family: sans-serif;
}

#controls {
    display: flex;
}

.slidecontainer {
    width: var(--graph-width);
}

.slider {
    --sliderSize: 1%;
    -webkit-appearance: none;  /* Override default CSS styles */
    appearance: none;
    width: 100%; /* Full-width */
    height: 25px; /* Specified height */
    background: #d3d3d3; /* Grey background */
    /*outline: none; !* Remove outline *!*/
}

/* The slider handle (use -webkit- (Chrome, Opera, Safari, Edge) and -moz- (Firefox) to override default look) */
.slider::-webkit-slider-thumb {
    -webkit-appearance: none; /* Override default look */
    appearance: none;
    width: var(--sliderSize); /* Set a specific slider handle width */
    height: 25px; /* Slider handle height */
    background: #04AA6D; /* Green background */
    cursor: pointer; /* Cursor on hover */
}

#canvas-container {
    width: var(--graph-width);
    height: calc(100vh - 4rem);
    /*border: 1px solid red;*/
}

#reading-selection, #reading-selection-toggle {
    position: absolute;
    background-color: white;
    right: 1rem;
    border: 2px solid grey;
}

#message-log {
    border: 1px solid grey;
    width: 20rem;
    height: 6rem;
    overflow-y: scroll;
    overflow-x: clip;
}

#commands-page {
    display: grid;
    grid-template-columns: 50% auto;
}

.radio {
    cursor: pointer;
}

.radio img {
    width: 20px;
    height: 20px;
}

/* Navigation Styles */
.navbar {
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.nav-content {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.file-selector select {
    padding: 5px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    min-width: 150px;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-link {
    text-decoration: none;
    color: #007bff;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.nav-link:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #0056b3;
}

.nav-link.active {
    background-color: #007bff;
    color: white;
}

.nav-separator {
    color: #6c757d;
    margin: 0 5px;
}

.loading-indicator {
    color: #6c757d;
    font-size: 0.9em;
}

/* Alert styles for error messages */
.alert {
    padding: 10px 15px;
    margin: 10px 0;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Loading Spinner Styles */
.loading-spinner {
    display: flex;
    align-items: center;
    gap: 10px;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-sm .spinner {
    width: 16px;
    height: 16px;
    border-width: 1px;
}

.spinner-md .spinner {
    width: 24px;
    height: 24px;
}

.spinner-lg .spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

.loading-text {
    color: #6c757d;
    font-size: 0.9em;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 4px 2px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Page-specific styles */
.graph-page {
    position: relative;
}

.reading-selection {
    position: absolute;
    background-color: white;
    right: 1rem;
    border: 2px solid grey;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 10;
}

.reading-selection-toggle {
    position: absolute;
    background-color: white;
    right: 1rem;
    top: 0;
    border: 2px solid grey;
    padding: 5px 10px;
    cursor: pointer;
    z-index: 11;
}

.reading-checkbox {
    display: block;
    margin: 5px 0;
    cursor: pointer;
}

.details-page {
    padding: 20px;
}

.detail-section {
    margin-bottom: 30px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.detail-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.commands-page {
    display: grid;
    grid-template-columns: 50% auto;
    gap: 20px;
    padding: 20px;
}

.command-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.command-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
}

.temperature-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.temperature-option {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.message-log {
    border: 1px solid grey;
    width: 100%;
    max-width: 20rem;
    height: 6rem;
    overflow-y: scroll;
    overflow-x: clip;
    padding: 10px;
    background-color: #f8f9fa;
    font-family: monospace;
    font-size: 0.9em;
}

.radio-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}