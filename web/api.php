<?php

require_once 'config.php';
require_once 'functions.php';

error_reporting(E_ALL); ini_set('display_errors', '1');

$cmd = $_GET['cmd'] ?? 'readings';

if ($cmd === 'readings') {

    $date = $_GET['date'] ?? null;

    print json_encode(fetchReadings($date));

} else if ($cmd === 'fetch-latest-readings') {

    $readings = fetchReadings(); // '2023-03-13'

    print json_encode(array_pop($readings));

} else if ($cmd === 'latest-dates') {
    print json_encode(getLatestDates());
} else if ($cmd === 'test') {
    $result = sendMessage(10, [5]);

    print json_encode($result ? 'ok' : 'nok');

} else if ($cmd === 'valve-enable') {
    $enable = intval($_GET['enable'] ?? 0);

    $result = sendMessage(20, [$enable]);

    print json_encode($result ? "ok: $enable" : 'nok');

} else if ($cmd === 'set-power') {
    $state = intval($_GET['state'] ?? 0);

    $result = sendMessage(60, [$state]);

    print json_encode($state);

} else if ($cmd === 'set-temperature') {
    $temp = intval($_GET['temp'] ?? 0);

    $result = sendMessage(30, [$temp + 128]);

    print json_encode($result ? "ok: $temp" : 'nok');

} else if ($cmd === 'fetch-message-log') {
    print json_encode(getMessages());

} else if ($cmd === 'fetch-ctrl-state') {
    print getCtrlState();

} else {
    print 'unknown command: '. $cmd;
}

function getLatestDates(): array {
    $query = "select distinct date(time, 'unixepoch', 'localtime') date from readings
        order by date desc limit 20";

    $stmt = getConnection()->prepare($query);

    $stmt->execute();

    $dates = $stmt->fetchAll(PDO::FETCH_NUM);

    return array_merge(...$dates);
}

function getConnection(): PDO {
    return new PDO('sqlite:' . getDataDir() . '/readings.sqlite');
}

function getMessages(): array {
    $path = getDataDir() . '/messages.txt';
    $lines = file($path);

    $result = [];

    foreach ($lines as $line) {
        $parts = explode(' ', $line, 3);
        $timestamp = $parts[0] . ' ' . $parts[1]; // "25-06-10 10:43"
        $message = $parts[2];
        $result[] = [ 'timestamp' => $timestamp, 'message' => $message ];
    }

    return array_reverse($result);
}

function getCtrlState(): string {
    $path = getSettingsDir() . '/heatpump.state.json';
    return file_get_contents($path);
}

function fetchReadings($date = null): array {
    $date = $date ?? 'now';

    $conn = getConnection();

    $stmt = $conn->prepare("
        SELECT * FROM readings
         WHERE time BETWEEN strftime('%s', datetime(:date, 'utc'))
            AND strftime('%s', datetime(:date, '+1 day', 'utc'))");

    $stmt->bindValue(':date', $date);

    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_NUM);
}
