<?php

function getDataDir(): string {
    $host = gethostname();

    if ($host === 'rock') {
        return '/home/<USER>';
    } else if ($host === 'pi') {
        return '/home/<USER>';
    } else if ($host === 'xu') {
        return 'data';
    } else {
        throw new Error('unknown host: -' . $host . '-');
    }
}

function getSettingsDir(): string {
    $host = gethostname();

    if ($host === 'rock') {
        return '/mnt/ramdisk';
    } else if ($host === 'xu') {
        return 'data';
    } else {
        throw new Error('unknown host: -' . $host . '-');
    }
}


